using ClassLibrary1.Model;

namespace ClassLibrary1
{
    public static class DataSeeder
    {

        // Phương thức để lấy giá vé theo hàng
        public static decimal GetSeatPrice(DaContext context, string seatRow)
        {
            var seatPrice = context.SeatPrices.FirstOrDefault(sp => sp.SeatRow == seatRow);
            return seatPrice?.Price ?? 0;
        }

        // Phương thức để lấy tất cả ghế của một phim
        public static List<Seat> GetSeatsForMovie(DaContext context, int movieId)
        {
            return context.Seats
                .Where(s => s.MovieId == movieId)
                .OrderBy(s => s.SeatRow)
                .ThenBy(s => s.SeatNumber)
                .ToList();
        }

        // Phương thức để đặt ghế
        public static bool BookSeat(DaContext context, int seatId)
        {
            var seat = context.Seats.Find(seatId);
            if (seat != null && !seat.IsBooked)
            {
                seat.IsBooked = true;
                seat.BookedDate = DateTime.Now;
                context.SaveChanges();
                return true;
            }
            return false;
        }

        // Phương thức để đặt nhiều ghế cùng lúc
        public static bool BookSeats(DaContext context, List<int> seatIds)
        {
            var seats = context.Seats.Where(s => seatIds.Contains(s.SeatId) && !s.IsBooked).ToList();

            if (seats.Count == seatIds.Count)
            {
                foreach (var seat in seats)
                {
                    seat.IsBooked = true;
                    seat.BookedDate = DateTime.Now;
                }
                context.SaveChanges();
                return true;
            }
            return false;
        }

        // Phương thức để lấy thống kê ghế
        public static (int totalSeats, int bookedSeats, int availableSeats) GetSeatStatistics(DaContext context, int movieId)
        {
            var seats = context.Seats.Where(s => s.MovieId == movieId).ToList();
            var totalSeats = seats.Count;
            var bookedSeats = seats.Count(s => s.IsBooked);
            var availableSeats = totalSeats - bookedSeats;

            return (totalSeats, bookedSeats, availableSeats);
        }
    }
}
