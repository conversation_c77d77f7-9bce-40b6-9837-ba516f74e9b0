using ClassLibrary1.Model;
using Microsoft.EntityFrameworkCore;

namespace ClassLibrary1
{
    public static class DataSeeder
    {
        public static void SeedData(DaContext context)
        {
            // Đảm bảo database được tạo
            context.Database.EnsureCreated();

            // Kiểm tra xem đã có dữ liệu chưa
            if (context.Movies.Any())
            {
                return; // Đã có dữ liệu rồi
            }

            // Tạo dữ liệu mẫu cho phim
            var movies = new List<Movie>
            {
                new Movie 
                { 
                    MovieName = "Avengers: Endgame", 
                    Description = "Siêu anh hùng Marvel tập hợp để đánh bại <PERSON>" 
                },
                new Movie 
                { 
                    MovieName = "Spider-Man: No Way Home", 
                    Description = "Peter Parker đối mặt với đa vũ trụ" 
                },
                new Movie 
                { 
                    MovieName = "Top Gun: Maverick", 
                    Description = "Phi công huyền thoại trở lại" 
                },
                new Movie 
                { 
                    MovieName = "Avatar: The Way of Water", 
                    Description = "Cuộc phiêu lưu mới trên hành tinh Pandora" 
                }
            };

            context.Movies.AddRange(movies);
            context.SaveChanges();

            // Tạo ghế cho mỗi phim (6 hàng x 10 ghế = 60 ghế/phim)
            var seats = new List<Seat>();
            var rows = new[] { "A", "B", "C", "D", "E", "F" };

            foreach (var movie in movies)
            {
                foreach (var row in rows)
                {
                    for (int seatNumber = 1; seatNumber <= 10; seatNumber++)
                    {
                        seats.Add(new Seat
                        {
                            MovieId = movie.MovieId,
                            SeatRow = row,
                            SeatNumber = seatNumber,
                            IsBooked = false
                        });
                    }
                }
            }

            context.Seats.AddRange(seats);
            context.SaveChanges();
        }

        // Phương thức để lấy giá vé theo hàng
        public static decimal GetSeatPrice(DaContext context, string seatRow)
        {
            var seatPrice = context.SeatPrices.FirstOrDefault(sp => sp.SeatRow == seatRow);
            return seatPrice?.Price ?? 0;
        }

        // Phương thức để lấy tất cả ghế của một phim
        public static List<Seat> GetSeatsForMovie(DaContext context, int movieId)
        {
            return context.Seats
                .Where(s => s.MovieId == movieId)
                .OrderBy(s => s.SeatRow)
                .ThenBy(s => s.SeatNumber)
                .ToList();
        }

        // Phương thức để đặt ghế
        public static bool BookSeat(DaContext context, int seatId)
        {
            var seat = context.Seats.Find(seatId);
            if (seat != null && !seat.IsBooked)
            {
                seat.IsBooked = true;
                seat.BookedDate = DateTime.Now;
                context.SaveChanges();
                return true;
            }
            return false;
        }

        // Phương thức để đặt nhiều ghế cùng lúc
        public static bool BookSeats(DaContext context, List<int> seatIds)
        {
            var seats = context.Seats.Where(s => seatIds.Contains(s.SeatId) && !s.IsBooked).ToList();
            
            if (seats.Count == seatIds.Count)
            {
                foreach (var seat in seats)
                {
                    seat.IsBooked = true;
                    seat.BookedDate = DateTime.Now;
                }
                context.SaveChanges();
                return true;
            }
            return false;
        }

        // Phương thức để lấy thống kê ghế
        public static (int totalSeats, int bookedSeats, int availableSeats) GetSeatStatistics(DaContext context, int movieId)
        {
            var seats = context.Seats.Where(s => s.MovieId == movieId).ToList();
            var totalSeats = seats.Count;
            var bookedSeats = seats.Count(s => s.IsBooked);
            var availableSeats = totalSeats - bookedSeats;

            return (totalSeats, bookedSeats, availableSeats);
        }
    }
}
