﻿// <auto-generated />
using System;
using ClassLibrary1;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace ClassLibrary1.Migrations
{
    [DbContext(typeof(DaContext))]
    [Migration("20250529091408_SeedMoviesAndSeats")]
    partial class SeedMoviesAndSeats
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.16")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("ClassLibrary1.Model.Movie", b =>
                {
                    b.Property<int>("MovieId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MovieId"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("MovieName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("MovieId");

                    b.ToTable("Movies");

                    b.HasData(
                        new
                        {
                            MovieId = 1,
                            CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Siêu anh hùng Marvel tập hợp để đánh bại Thanos",
                            MovieName = "Avengers: Endgame"
                        },
                        new
                        {
                            MovieId = 2,
                            CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Peter Parker đối mặt với đa vũ trụ",
                            MovieName = "Spider-Man: No Way Home"
                        });
                });

            modelBuilder.Entity("ClassLibrary1.Model.Seat", b =>
                {
                    b.Property<int>("SeatId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SeatId"));

                    b.Property<DateTime?>("BookedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsBooked")
                        .HasColumnType("bit");

                    b.Property<int>("MovieId")
                        .HasColumnType("int");

                    b.Property<int>("SeatNumber")
                        .HasColumnType("int");

                    b.Property<string>("SeatRow")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.HasKey("SeatId");

                    b.HasIndex("MovieId", "SeatRow", "SeatNumber")
                        .IsUnique();

                    b.ToTable("Seats");

                    b.HasData(
                        new
                        {
                            SeatId = 1,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 1,
                            SeatRow = "A"
                        },
                        new
                        {
                            SeatId = 2,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 2,
                            SeatRow = "A"
                        },
                        new
                        {
                            SeatId = 3,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 3,
                            SeatRow = "A"
                        },
                        new
                        {
                            SeatId = 4,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 4,
                            SeatRow = "A"
                        },
                        new
                        {
                            SeatId = 5,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 5,
                            SeatRow = "A"
                        },
                        new
                        {
                            SeatId = 6,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 1,
                            SeatRow = "B"
                        },
                        new
                        {
                            SeatId = 7,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 2,
                            SeatRow = "B"
                        },
                        new
                        {
                            SeatId = 8,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 3,
                            SeatRow = "B"
                        },
                        new
                        {
                            SeatId = 9,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 4,
                            SeatRow = "B"
                        },
                        new
                        {
                            SeatId = 10,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 5,
                            SeatRow = "B"
                        },
                        new
                        {
                            SeatId = 11,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 1,
                            SeatRow = "C"
                        },
                        new
                        {
                            SeatId = 12,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 2,
                            SeatRow = "C"
                        },
                        new
                        {
                            SeatId = 13,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 3,
                            SeatRow = "C"
                        },
                        new
                        {
                            SeatId = 14,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 4,
                            SeatRow = "C"
                        },
                        new
                        {
                            SeatId = 15,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 5,
                            SeatRow = "C"
                        },
                        new
                        {
                            SeatId = 16,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 1,
                            SeatRow = "D"
                        },
                        new
                        {
                            SeatId = 17,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 2,
                            SeatRow = "D"
                        },
                        new
                        {
                            SeatId = 18,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 3,
                            SeatRow = "D"
                        },
                        new
                        {
                            SeatId = 19,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 4,
                            SeatRow = "D"
                        },
                        new
                        {
                            SeatId = 20,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 5,
                            SeatRow = "D"
                        },
                        new
                        {
                            SeatId = 21,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 1,
                            SeatRow = "E"
                        },
                        new
                        {
                            SeatId = 22,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 2,
                            SeatRow = "E"
                        },
                        new
                        {
                            SeatId = 23,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 3,
                            SeatRow = "E"
                        },
                        new
                        {
                            SeatId = 24,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 4,
                            SeatRow = "E"
                        },
                        new
                        {
                            SeatId = 25,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 5,
                            SeatRow = "E"
                        },
                        new
                        {
                            SeatId = 26,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 1,
                            SeatRow = "F"
                        },
                        new
                        {
                            SeatId = 27,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 2,
                            SeatRow = "F"
                        },
                        new
                        {
                            SeatId = 28,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 3,
                            SeatRow = "F"
                        },
                        new
                        {
                            SeatId = 29,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 4,
                            SeatRow = "F"
                        },
                        new
                        {
                            SeatId = 30,
                            IsBooked = false,
                            MovieId = 1,
                            SeatNumber = 5,
                            SeatRow = "F"
                        },
                        new
                        {
                            SeatId = 31,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 1,
                            SeatRow = "A"
                        },
                        new
                        {
                            SeatId = 32,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 2,
                            SeatRow = "A"
                        },
                        new
                        {
                            SeatId = 33,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 3,
                            SeatRow = "A"
                        },
                        new
                        {
                            SeatId = 34,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 4,
                            SeatRow = "A"
                        },
                        new
                        {
                            SeatId = 35,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 5,
                            SeatRow = "A"
                        },
                        new
                        {
                            SeatId = 36,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 1,
                            SeatRow = "B"
                        },
                        new
                        {
                            SeatId = 37,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 2,
                            SeatRow = "B"
                        },
                        new
                        {
                            SeatId = 38,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 3,
                            SeatRow = "B"
                        },
                        new
                        {
                            SeatId = 39,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 4,
                            SeatRow = "B"
                        },
                        new
                        {
                            SeatId = 40,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 5,
                            SeatRow = "B"
                        },
                        new
                        {
                            SeatId = 41,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 1,
                            SeatRow = "C"
                        },
                        new
                        {
                            SeatId = 42,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 2,
                            SeatRow = "C"
                        },
                        new
                        {
                            SeatId = 43,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 3,
                            SeatRow = "C"
                        },
                        new
                        {
                            SeatId = 44,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 4,
                            SeatRow = "C"
                        },
                        new
                        {
                            SeatId = 45,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 5,
                            SeatRow = "C"
                        },
                        new
                        {
                            SeatId = 46,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 1,
                            SeatRow = "D"
                        },
                        new
                        {
                            SeatId = 47,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 2,
                            SeatRow = "D"
                        },
                        new
                        {
                            SeatId = 48,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 3,
                            SeatRow = "D"
                        },
                        new
                        {
                            SeatId = 49,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 4,
                            SeatRow = "D"
                        },
                        new
                        {
                            SeatId = 50,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 5,
                            SeatRow = "D"
                        },
                        new
                        {
                            SeatId = 51,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 1,
                            SeatRow = "E"
                        },
                        new
                        {
                            SeatId = 52,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 2,
                            SeatRow = "E"
                        },
                        new
                        {
                            SeatId = 53,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 3,
                            SeatRow = "E"
                        },
                        new
                        {
                            SeatId = 54,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 4,
                            SeatRow = "E"
                        },
                        new
                        {
                            SeatId = 55,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 5,
                            SeatRow = "E"
                        },
                        new
                        {
                            SeatId = 56,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 1,
                            SeatRow = "F"
                        },
                        new
                        {
                            SeatId = 57,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 2,
                            SeatRow = "F"
                        },
                        new
                        {
                            SeatId = 58,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 3,
                            SeatRow = "F"
                        },
                        new
                        {
                            SeatId = 59,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 4,
                            SeatRow = "F"
                        },
                        new
                        {
                            SeatId = 60,
                            IsBooked = false,
                            MovieId = 2,
                            SeatNumber = 5,
                            SeatRow = "F"
                        });
                });

            modelBuilder.Entity("ClassLibrary1.Model.SeatPrice", b =>
                {
                    b.Property<int>("SeatPriceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SeatPriceId"));

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SeatRow")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.HasKey("SeatPriceId");

                    b.HasIndex("SeatRow")
                        .IsUnique();

                    b.ToTable("SeatPrices");

                    b.HasData(
                        new
                        {
                            SeatPriceId = 1,
                            Price = 25000m,
                            SeatRow = "A"
                        },
                        new
                        {
                            SeatPriceId = 2,
                            Price = 30000m,
                            SeatRow = "B"
                        },
                        new
                        {
                            SeatPriceId = 3,
                            Price = 35000m,
                            SeatRow = "C"
                        },
                        new
                        {
                            SeatPriceId = 4,
                            Price = 40000m,
                            SeatRow = "D"
                        },
                        new
                        {
                            SeatPriceId = 5,
                            Price = 50000m,
                            SeatRow = "E"
                        },
                        new
                        {
                            SeatPriceId = 6,
                            Price = 45000m,
                            SeatRow = "F"
                        });
                });

            modelBuilder.Entity("ClassLibrary1.Model.Seat", b =>
                {
                    b.HasOne("ClassLibrary1.Model.Movie", "Movie")
                        .WithMany("Seats")
                        .HasForeignKey("MovieId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Movie");
                });

            modelBuilder.Entity("ClassLibrary1.Model.Movie", b =>
                {
                    b.Navigation("Seats");
                });
#pragma warning restore 612, 618
        }
    }
}
