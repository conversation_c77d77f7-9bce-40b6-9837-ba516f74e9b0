﻿using Microsoft.EntityFrameworkCore;

namespace ClassLibrary1
{
    public class DaContext : DbContext
    {
        public DaContext(DbContextOptions<DaContext> options) : base(options)
        {

        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                optionsBuilder.UseSqlServer("Server=.\\sqlexpress;Database=DaContext;Trusted_Connection=True;TrustServerCertificate=True");
            }
        }
    }
}
