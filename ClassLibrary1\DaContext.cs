﻿using Microsoft.EntityFrameworkCore;
using ClassLibrary1.Model;

namespace ClassLibrary1
{
    public class DaContext : DbContext
    {

        public DaContext()
        {

        }
        public DaContext(DbContextOptions<DaContext> options) : base(options)
        {

        }

        // DbSets cho các bảng
        public DbSet<Movie> Movies { get; set; }
        public DbSet<Seat> Seats { get; set; }
        public DbSet<SeatPrice> SeatPrices { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                optionsBuilder.UseSqlServer("Server=.\\sqlexpress;Database=MyWinformProjectDB;Trusted_Connection=True;TrustServerCertificate=True");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // <PERSON><PERSON>u hì<PERSON> quan hệ
            modelBuilder.Entity<Seat>()
                .HasOne(s => s.Movie)
                .WithMany(m => m.Seats)
                .HasForeignKey(s => s.MovieId)
                .OnDelete(DeleteBehavior.Cascade);

            // Tạo index cho tìm kiếm nhanh
            modelBuilder.Entity<Seat>()
                .HasIndex(s => new { s.MovieId, s.SeatRow, s.SeatNumber })
                .IsUnique();

            modelBuilder.Entity<SeatPrice>()
                .HasIndex(sp => sp.SeatRow)
                .IsUnique();

            // Seed data cho giá vé
            modelBuilder.Entity<SeatPrice>().HasData(
                new SeatPrice { SeatPriceId = 1, SeatRow = "A", Price = 25000 },
                new SeatPrice { SeatPriceId = 2, SeatRow = "B", Price = 30000 },
                new SeatPrice { SeatPriceId = 3, SeatRow = "C", Price = 35000 },
                new SeatPrice { SeatPriceId = 4, SeatRow = "D", Price = 40000 },
                new SeatPrice { SeatPriceId = 5, SeatRow = "E", Price = 50000 },
                new SeatPrice { SeatPriceId = 6, SeatRow = "F", Price = 45000 }
            );

            // Seed data cho phim
            modelBuilder.Entity<Movie>().HasData(
                new Movie
                {
                    MovieId = 1,
                    MovieName = "Avengers: Endgame",
                    Description = "Siêu anh hùng Marvel tập hợp để đánh bại Thanos",
                    CreatedDate = new DateTime(2024, 1, 1)
                },
                new Movie
                {
                    MovieId = 2,
                    MovieName = "Spider-Man: No Way Home",
                    Description = "Peter Parker đối mặt với đa vũ trụ",
                    CreatedDate = new DateTime(2024, 1, 1)
                }
            );

            // Seed data cho ghế - 5 ghế mỗi hàng cho cả 2 phim
            var seats = new List<Seat>();
            var seatId = 1;

            // Tạo ghế cho 2 phim
            for (int movieId = 1; movieId <= 2; movieId++)
            {
                // Tạo ghế cho 6 hàng (A, B, C, D, E, F)
                for (char row = 'A'; row <= 'F'; row++)
                {
                    // Tạo 5 ghế cho mỗi hàng
                    for (int seatNumber = 1; seatNumber <= 5; seatNumber++)
                    {
                        seats.Add(new Seat
                        {
                            SeatId = seatId++,
                            MovieId = movieId,
                            SeatRow = row.ToString(),
                            SeatNumber = seatNumber,
                            IsBooked = false,
                            BookedDate = null
                        });
                    }
                }
            }

            modelBuilder.Entity<Seat>().HasData(seats);
        }
    }
}
