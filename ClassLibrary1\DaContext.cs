﻿using Microsoft.EntityFrameworkCore;
using ClassLibrary1.Model;

namespace ClassLibrary1
{
    public class DaContext : DbContext
    {

        public DaContext()
        {
                
        }
        public DaContext(DbContextOptions<DaContext> options) : base(options)
        {

        }

        // DbSets cho các bảng
        public DbSet<Movie> Movies { get; set; }
        public DbSet<Seat> Seats { get; set; }
        public DbSet<SeatPrice> SeatPrices { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                optionsBuilder.UseSqlServer("Server=.\\sqlexpress;Database=MyWinformProjectDB;Trusted_Connection=True;TrustServerCertificate=True");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // <PERSON><PERSON>u hì<PERSON> quan hệ
            modelBuilder.Entity<Seat>()
                .HasOne(s => s.Movie)
                .WithMany(m => m.Seats)
                .HasForeignKey(s => s.MovieId)
                .OnDelete(DeleteBehavior.Cascade);

            // Tạo index cho tìm kiếm nhanh
            modelBuilder.Entity<Seat>()
                .HasIndex(s => new { s.MovieId, s.SeatRow, s.SeatNumber })
                .IsUnique();

            modelBuilder.Entity<SeatPrice>()
                .HasIndex(sp => sp.SeatRow)
                .IsUnique();

            // Seed data cho giá vé
            modelBuilder.Entity<SeatPrice>().HasData(
                new SeatPrice { SeatPriceId = 1, SeatRow = "A", Price = 25000 },
                new SeatPrice { SeatPriceId = 2, SeatRow = "B", Price = 30000 },
                new SeatPrice { SeatPriceId = 3, SeatRow = "C", Price = 35000 },
                new SeatPrice { SeatPriceId = 4, SeatRow = "D", Price = 40000 },
                new SeatPrice { SeatPriceId = 5, SeatRow = "E", Price = 50000 },
                new SeatPrice { SeatPriceId = 6, SeatRow = "F", Price = 45000 }
            );
        }
    }
}
