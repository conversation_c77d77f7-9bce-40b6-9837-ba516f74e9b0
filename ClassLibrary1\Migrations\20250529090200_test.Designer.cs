﻿// <auto-generated />
using System;
using ClassLibrary1;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace ClassLibrary1.Migrations
{
    [DbContext(typeof(DaContext))]
    [Migration("20250529090200_test")]
    partial class test
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.16")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("ClassLibrary1.Model.Movie", b =>
                {
                    b.Property<int>("MovieId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MovieId"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("MovieName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("MovieId");

                    b.ToTable("Movies");
                });

            modelBuilder.Entity("ClassLibrary1.Model.Seat", b =>
                {
                    b.Property<int>("SeatId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SeatId"));

                    b.Property<DateTime?>("BookedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsBooked")
                        .HasColumnType("bit");

                    b.Property<int>("MovieId")
                        .HasColumnType("int");

                    b.Property<int>("SeatNumber")
                        .HasColumnType("int");

                    b.Property<string>("SeatRow")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.HasKey("SeatId");

                    b.HasIndex("MovieId", "SeatRow", "SeatNumber")
                        .IsUnique();

                    b.ToTable("Seats");
                });

            modelBuilder.Entity("ClassLibrary1.Model.SeatPrice", b =>
                {
                    b.Property<int>("SeatPriceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SeatPriceId"));

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SeatRow")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.HasKey("SeatPriceId");

                    b.HasIndex("SeatRow")
                        .IsUnique();

                    b.ToTable("SeatPrices");

                    b.HasData(
                        new
                        {
                            SeatPriceId = 1,
                            Price = 25000m,
                            SeatRow = "A"
                        },
                        new
                        {
                            SeatPriceId = 2,
                            Price = 30000m,
                            SeatRow = "B"
                        },
                        new
                        {
                            SeatPriceId = 3,
                            Price = 35000m,
                            SeatRow = "C"
                        },
                        new
                        {
                            SeatPriceId = 4,
                            Price = 40000m,
                            SeatRow = "D"
                        },
                        new
                        {
                            SeatPriceId = 5,
                            Price = 50000m,
                            SeatRow = "E"
                        },
                        new
                        {
                            SeatPriceId = 6,
                            Price = 45000m,
                            SeatRow = "F"
                        });
                });

            modelBuilder.Entity("ClassLibrary1.Model.Seat", b =>
                {
                    b.HasOne("ClassLibrary1.Model.Movie", "Movie")
                        .WithMany("Seats")
                        .HasForeignKey("MovieId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Movie");
                });

            modelBuilder.Entity("ClassLibrary1.Model.Movie", b =>
                {
                    b.Navigation("Seats");
                });
#pragma warning restore 612, 618
        }
    }
}
