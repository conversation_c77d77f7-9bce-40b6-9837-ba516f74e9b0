﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ClassLibrary1.Model
{
    public class Movie
    {
        [Key]
        public int MovieId { get; set; }

        [Required]
        [StringLength(200)]
        public string MovieName { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public virtual ICollection<Seat> Seats { get; set; } = new List<Seat>();
    }

    public class Seat
    {
        [Key]
        public int SeatId { get; set; }

        [Required]
        public int MovieId { get; set; }

        [Required]
        [StringLength(10)]
        public string SeatRow { get; set; } // A, B, C, D, E, F

        [Required]
        public int SeatNumber { get; set; } // 1, 2, 3, 4, 5, 6, 7, 8, 9, 10

        [Required]
        public bool IsBooked { get; set; } = false; // false: còn trống, true: đã bán

        public DateTime? BookedDate { get; set; }

        [ForeignKey("MovieId")]
        public virtual Movie Movie { get; set; }
    }

    public class SeatPrice
    {
        [Key]
        public int SeatPriceId { get; set; }

        [Required]
        [StringLength(10)]
        public string SeatRow { get; set; } // A, B, C, D, E, F

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }
    }
}
