# 🎬 CHƯƠNG TRÌNH BÁN VÉ XEM PHIM

## 📋 Tổng quan
Chương trình WinForms đơn giản để bán vé xem phim với đầy đủ chức năng theo yêu cầu:
- Chọn phim từ ComboBox
- Hiể<PERSON> thị sơ đồ ghế 6 hàng x 10 ghế (A1-F10)
- Chọn/bỏ chọn ghế với màu sắc trực quan
- Tính tổng tiền tự động
- Tooltip hiển thị giá vé
- Thanh toán và lưu vào database

## 🎯 Các tính năng đã hoàn thành

### ✅ Giao diện
- **Tiêu đề**: "Chương trình bán vé xem phim"
- **Vị trí**: Form xuất hiện giữa màn hình
- **Responsive**: ComboBox và ghế thay đổi kích thước theo form
- **Nút thanh toán**: <PERSON><PERSON><PERSON> bám sát góc dưới bên phải

### ✅ Chức năng chọn ghế
- **<PERSON><PERSON>u ghế trống**: <PERSON><PERSON><PERSON> lá (LightGreen)
- **Màu ghế đang chọn**: Xanh dương (Blue)
- **Màu ghế đã bán**: Đỏ (Red) - không thể chọn
- **Click để chọn/bỏ chọn**: Toggle trạng thái ghế
- **Tính tiền tự động**: Cập nhật ngay khi chọn/bỏ chọn

### ✅ Tooltip giá vé
- **Hàng A**: 25.000đ
- **Hàng B**: 30.000đ
- **Hàng C**: 35.000đ
- **Hàng D**: 40.000đ
- **Hàng E**: 50.000đ
- **Hàng F**: 45.000đ

### ✅ Thanh toán
- **Kiểm tra**: Phải chọn ít nhất 1 ghế
- **Xác nhận**: MessageBox hiển thị số ghế và tổng tiền
- **Lưu database**: Cập nhật trạng thái ghế đã bán
- **Reset**: Xóa selection và reset tổng tiền về 0đ

### ✅ Database
- **Entity Framework**: Sử dụng EF Core trực tiếp
- **3 bảng**: Movies, Seats, SeatPrices
- **Dữ liệu mẫu**: 4 phim, mỗi phim 60 ghế
- **Tự động tạo**: Database và data khi chạy lần đầu

## 🚀 Cách chạy chương trình

### 1. Chạy từ Visual Studio
```
1. Mở solution WinFormsApp1.sln
2. Set WinFormsApp1 làm Startup Project
3. Nhấn F5 hoặc Debug > Start Debugging
```

### 2. Chạy từ Command Line
```bash
cd WinFormsApp1
dotnet run --project WinFormsApp1.csproj
```

### 3. Build Release
```bash
dotnet build --configuration Release
cd WinFormsApp1\bin\Release\net8.0-windows
WinFormsApp1.exe
```

## 🎮 Hướng dẫn sử dụng

### Bước 1: Chọn phim
- Mở ứng dụng, ComboBox sẽ hiển thị danh sách phim
- Chọn phim muốn đặt vé

### Bước 2: Chọn ghế
- Sơ đồ ghế sẽ hiển thị 6 hàng A-F, mỗi hàng 10 ghế
- **Ghế xanh lá**: Còn trống, có thể chọn
- **Ghế đỏ**: Đã bán, không thể chọn
- Di chuột vào ghế để xem giá vé
- Click ghế để chọn (chuyển màu xanh dương)
- Click lại để bỏ chọn (chuyển về màu xanh lá)

### Bước 3: Xem tổng tiền
- Tổng tiền hiển thị ở góc dưới bên trái
- Tự động cập nhật khi chọn/bỏ chọn ghế

### Bước 4: Thanh toán
- Click nút "Thanh toán" ở góc dưới bên phải
- Xác nhận trong MessageBox
- Ghế đã mua sẽ chuyển màu đỏ và không thể chọn lại

## 🗄️ Cấu trúc Database

### Connection String
```
Server=.\\sqlexpress;Database=MyWinformProjectDB;Trusted_Connection=True;TrustServerCertificate=True
```

### Bảng Movies
- MovieId (PK)
- MovieName
- Description
- CreatedDate

### Bảng Seats
- SeatId (PK)
- MovieId (FK)
- SeatRow (A-F)
- SeatNumber (1-10)
- IsBooked (true/false)
- BookedDate

### Bảng SeatPrices
- SeatPriceId (PK)
- SeatRow (A-F)
- Price (25000-50000)

## 🔧 Cấu trúc Code

### Form1.cs - Logic chính
- `Form1_Load()`: Khởi tạo dữ liệu
- `LoadMovies()`: Load danh sách phim
- `cbphim_SelectedIndexChanged()`: Xử lý khi đổi phim
- `LoadSeatsForMovie()`: Tạo sơ đồ ghế
- `SeatButton_Click()`: Xử lý click ghế
- `UpdateTotalPrice()`: Tính tổng tiền
- `button3_Click()`: Xử lý thanh toán

### ClassLibrary1 - Data Layer
- `DaContext.cs`: Entity Framework DbContext
- `Model/Class1.cs`: Các model Movie, Seat, SeatPrice
- `DataSeeder.cs`: Tạo dữ liệu mẫu

## 🎨 Màu sắc và UI
- **Form background**: Mặc định Windows
- **Ghế trống**: Color.LightGreen
- **Ghế đang chọn**: Color.Blue
- **Ghế đã bán**: Color.Red
- **Nút thanh toán**: Xanh lá với chữ trắng
- **Tổng tiền**: Đỏ, font đậm 14pt

## 📝 Lưu ý
- Code được viết đơn giản, phù hợp sinh viên năm nhất
- Sử dụng Entity Framework trực tiếp, không có service layer
- Database tự động tạo khi chạy lần đầu
- Dữ liệu mẫu sẵn có để test ngay

## 🐛 Troubleshooting
- **Lỗi database**: Kiểm tra SQL Server đã chạy chưa
- **Lỗi build**: Restore NuGet packages: `dotnet restore`
- **Lỗi runtime**: Kiểm tra connection string trong DaContext.cs
