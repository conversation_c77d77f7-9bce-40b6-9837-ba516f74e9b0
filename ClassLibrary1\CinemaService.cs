using ClassLibrary1.Model;
using Microsoft.EntityFrameworkCore;

namespace ClassLibrary1
{
    /// <summary>
    /// Service class để xử lý logic nghiệp vụ cho chương trình bán vé xem phim
    /// </summary>
    public class CinemaService : IDisposable
    {
        private readonly DaContext _context;

        public CinemaService()
        {
            _context = new DaContext();
            // Đảm bảo database được tạo và có dữ liệu
            DataSeeder.SeedData(_context);
        }

        /// <summary>
        /// Lấy danh sách tất cả phim
        /// </summary>
        public List<Movie> GetAllMovies()
        {
            return _context.Movies.OrderBy(m => m.MovieName).ToList();
        }

        /// <summary>
        /// Lấy thông tin phim theo ID
        /// </summary>
        public Movie GetMovieById(int movieId)
        {
            return _context.Movies.FirstOrDefault(m => m.MovieId == movieId);
        }

        /// <summary>
        /// L<PERSON>y tất cả ghế của một phim với thông tin giá vé
        /// </summary>
        public List<SeatInfo> GetSeatsWithPriceForMovie(int movieId)
        {
            var query = from seat in _context.Seats
                        join price in _context.SeatPrices on seat.SeatRow equals price.SeatRow
                        where seat.MovieId == movieId
                        orderby seat.SeatRow, seat.SeatNumber
                        select new SeatInfo
                        {
                            SeatId = seat.SeatId,
                            MovieId = seat.MovieId,
                            SeatRow = seat.SeatRow,
                            SeatNumber = seat.SeatNumber,
                            IsBooked = seat.IsBooked,
                            BookedDate = seat.BookedDate,
                            Price = price.Price,
                            SeatCode = seat.SeatRow + seat.SeatNumber.ToString()
                        };

            return query.ToList();
        }

        /// <summary>
        /// Lấy giá vé theo hàng ghế
        /// </summary>
        public decimal GetSeatPrice(string seatRow)
        {
            var seatPrice = _context.SeatPrices.FirstOrDefault(sp => sp.SeatRow == seatRow);
            return seatPrice?.Price ?? 0;
        }

        /// <summary>
        /// Đặt một ghế
        /// </summary>
        public bool BookSeat(int seatId)
        {
            var seat = _context.Seats.Find(seatId);
            if (seat != null && !seat.IsBooked)
            {
                seat.IsBooked = true;
                seat.BookedDate = DateTime.Now;
                _context.SaveChanges();
                return true;
            }
            return false;
        }

        /// <summary>
        /// Đặt nhiều ghế cùng lúc (sử dụng transaction)
        /// </summary>
        public bool BookSeats(List<int> seatIds)
        {
            using var transaction = _context.Database.BeginTransaction();
            try
            {
                var seats = _context.Seats.Where(s => seatIds.Contains(s.SeatId) && !s.IsBooked).ToList();

                if (seats.Count != seatIds.Count)
                {
                    // Có ghế đã được đặt hoặc không tồn tại
                    return false;
                }

                foreach (var seat in seats)
                {
                    seat.IsBooked = true;
                    seat.BookedDate = DateTime.Now;
                }

                _context.SaveChanges();
                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                return false;
            }
        }

        /// <summary>
        /// Lấy thống kê ghế của một phim
        /// </summary>
        public SeatStatistics GetSeatStatistics(int movieId)
        {
            var seats = _context.Seats.Where(s => s.MovieId == movieId).ToList();

            return new SeatStatistics
            {
                TotalSeats = seats.Count,
                BookedSeats = seats.Count(s => s.IsBooked),
                AvailableSeats = seats.Count(s => !s.IsBooked)
            };
        }

        /// <summary>
        /// Tính tổng tiền cho danh sách ghế
        /// </summary>
        public decimal CalculateTotalPrice(List<int> seatIds)
        {
            var query = from seat in _context.Seats
                        join price in _context.SeatPrices on seat.SeatRow equals price.SeatRow
                        where seatIds.Contains(seat.SeatId)
                        select price.Price;

            return query.Sum();
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    /// <summary>
    /// Class chứa thông tin ghế kèm giá vé
    /// </summary>
    public class SeatInfo
    {
        public int SeatId { get; set; }
        public int MovieId { get; set; }
        public string SeatRow { get; set; }
        public int SeatNumber { get; set; }
        public bool IsBooked { get; set; }
        public DateTime? BookedDate { get; set; }
        public decimal Price { get; set; }
        public string SeatCode { get; set; }
    }

    /// <summary>
    /// Class chứa thống kê ghế
    /// </summary>
    public class SeatStatistics
    {
        public int TotalSeats { get; set; }
        public int BookedSeats { get; set; }
        public int AvailableSeats { get; set; }
    }
}
