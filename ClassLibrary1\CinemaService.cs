using ClassLibrary1.Model;

namespace ClassLibrary1
{
    public class CinemaService : IDisposable
    {
        private readonly DaContext _context;

        public CinemaService()
        {
            _context = new DaContext();
            _context.Database.EnsureCreated();
        }

        public List<Movie> GetAllMovies()
        {
            return _context.Movies.OrderBy(m => m.MovieName).ToList();
        }

        public Movie GetMovieById(int movieId)
        {
            return _context.Movies.FirstOrDefault(m => m.MovieId == movieId);
        }

        public List<SeatInfo> GetSeatsWithPriceForMovie(int movieId)
        {
            var query = from seat in _context.Seats
                        join price in _context.SeatPrices on seat.SeatRow equals price.SeatRow
                        where seat.MovieId == movieId
                        orderby seat.SeatRow, seat.SeatNumber
                        select new SeatInfo
                        {
                            SeatId = seat.SeatId,
                            MovieId = seat.MovieId,
                            SeatRow = seat.SeatRow,
                            SeatNumber = seat.SeatNumber,
                            IsBooked = seat.IsBooked,
                            BookedDate = seat.BookedDate,
                            Price = price.Price,
                            SeatCode = seat.SeatRow + seat.SeatNumber.ToString()
                        };

            return query.ToList();
        }

        public decimal GetSeatPrice(string seatRow)
        {
            var seatPrice = _context.SeatPrices.FirstOrDefault(sp => sp.SeatRow == seatRow);
            return seatPrice?.Price ?? 0;
        }

        public bool BookSeat(int seatId)
        {
            var seat = _context.Seats.Find(seatId);
            if (seat != null && !seat.IsBooked)
            {
                seat.IsBooked = true;
                seat.BookedDate = DateTime.Now;
                _context.SaveChanges();
                return true;
            }
            return false;
        }

        public bool BookSeats(List<int> seatIds)
        {
            using var transaction = _context.Database.BeginTransaction();
            try
            {
                var seats = _context.Seats.Where(s => seatIds.Contains(s.SeatId) && !s.IsBooked).ToList();

                if (seats.Count != seatIds.Count)
                {
                    return false;
                }

                foreach (var seat in seats)
                {
                    seat.IsBooked = true;
                    seat.BookedDate = DateTime.Now;
                }

                _context.SaveChanges();
                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                return false;
            }
        }


        public SeatStatistics GetSeatStatistics(int movieId)
        {
            var seats = _context.Seats.Where(s => s.MovieId == movieId).ToList();

            return new SeatStatistics
            {
                TotalSeats = seats.Count,
                BookedSeats = seats.Count(s => s.IsBooked),
                AvailableSeats = seats.Count(s => !s.IsBooked)
            };
        }

        public decimal CalculateTotalPrice(List<int> seatIds)
        {
            var query = from seat in _context.Seats
                        join price in _context.SeatPrices on seat.SeatRow equals price.SeatRow
                        where seatIds.Contains(seat.SeatId)
                        select price.Price;

            return query.Sum();
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    public class SeatInfo
    {
        public int SeatId { get; set; }
        public int MovieId { get; set; }
        public string SeatRow { get; set; }
        public int SeatNumber { get; set; }
        public bool IsBooked { get; set; }
        public DateTime? BookedDate { get; set; }
        public decimal Price { get; set; }
        public string SeatCode { get; set; }
    }

    public class SeatStatistics
    {
        public int TotalSeats { get; set; }
        public int BookedSeats { get; set; }
        public int AvailableSeats { get; set; }
    }
}
