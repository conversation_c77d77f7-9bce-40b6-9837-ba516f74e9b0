using ClassLibrary1;
using ClassLibrary1.Model;
using System.Drawing;
using System.Drawing.Text;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace WinFormsApp1
{
    public partial class Form1 : Form
    {
        private readonly DaContext _dbcontext;
        private readonly CinemaService _cinemaService;
        private double thanhtien { get; set; } = 0;


        public Form1()
        {
            InitializeComponent();
            _dbcontext = new DaContext();
            _cinemaService = new CinemaService();
            //toolTip1.SetToolTip(button1, "Đây là nút thanh toán - Click để xác nhận mua vé");
            //toolTip1.SetToolTip(button2, "Đ<PERSON>y là nút thanh toán - Click để xác nhận mua vé");

            Load_DuLieu();
        }



        public void Load_DuLieu()
        {
            khungghe.Controls.Clear();
            khungghe.AutoSize = true;
            khungghe.Dock = DockStyle.Fill;

            cbphim.DataSource = _cinemaService.GetAllMovies();
            cbphim.DisplayMember = "MovieName";
            cbphim.ValueMember = "MovieId";
        }





        private void button3_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("Bạn có chắc chắn muốn thanh toán hay không?", "Xác nhận thanh toán", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                MessageBox.Show("Thanh toán thành công!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
                //button1.BackColor = Color.White;
            }
        }

        private void tableLayoutPanel1_Paint(object sender, PaintEventArgs e)
        {

        }


        private void button2_Click(object sender, EventArgs e)
        {

        }

        private void cbphim_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cbphim.SelectedIndex == -1) Load_DuLieu();
            else
            {
                var selectedMovie = (Movie)cbphim.SelectedItem;
                var seats = _cinemaService.GetSeatsWithPriceForMovie(selectedMovie.MovieId);
                khungghe.Controls.Clear();
                khungghe.RowCount = 6;
                khungghe.ColumnCount = 10;

                khungghe.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 10F)); // Mỗi cột 10%


                foreach (var seat in seats)
                {
                    Button seatButton = new Button
                    {
                        Text = seat.SeatCode,
                        BackColor = seat.IsBooked ? Color.Red : Color.White,
                        Dock = DockStyle.Fill,
                        Tag = seat.SeatId
                    };
                    seatButton.Click += SeatButton_Click;
                    khungghe.Controls.Add(seatButton, seat.SeatNumber - 1, "ABCDEF".IndexOf(seat.SeatRow));
                }
            }

        }

        private void SeatButton_Click(object sender, EventArgs e)
        {
            Button btn = sender as Button;
            if (btn.BackColor == Color.White)
            {
                btn.BackColor = Color.Green;
                //thanhtien += _cinemaService.GetSeatPriceByRow(btn.Text.Substring(0, 1));
            }
            else btn.BackColor = Color.White;
        }
    }
}