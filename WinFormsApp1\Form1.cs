using ClassLibrary1;
using ClassLibrary1.Model;

namespace WinFormsApp1
{
    public partial class Form1 : Form
    {
        private DaContext context;
        private List<int> selectedSeats = new List<int>(); // Danh sách ghế đã chọn
        private Dictionary<string, decimal> seatPrices; // Giá vé theo hàng

        public Form1()
        {
            InitializeComponent();
            context = new DaContext();

            seatPrices = new Dictionary<string, decimal>
            {
                {"A", 25000},
                {"B", 30000},
                {"C", 35000},
                {"D", 40000},
                {"E", 50000},
                {"F", 45000}
            };
        }



        private void Form1_Load(object sender, EventArgs e)
        {
            LoadMovies();
        }

        private void LoadMovies()
        {
            var movies = context.Movies.ToList();

            // Tạm thời tắt event để tránh trigger khi đang load data
            //cbphim.SelectedIndexChanged -= cbphim_SelectedIndexChanged;

            cbphim.DataSource = movies;
            cbphim.DisplayMember = "MovieName";
            cbphim.ValueMember = "MovieId";

            // Bật lại event
            cbphim.SelectedIndexChanged += cbphim_SelectedIndexChanged;

            if (movies.Count > 0)
            {
                cbphim.SelectedIndex = 0;
                // Manually trigger load seats for first movie
                LoadSeatsForMovie(movies[0].MovieId);
                UpdateTotalPrice();
            }
        }





        private void button3_Click(object sender, EventArgs e)
        {
            if (selectedSeats.Count == 0)
            {
                MessageBox.Show("Vui lòng chọn ít nhất một ghế!", "Thông báo",
                               MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Tính tổng tiền
            decimal totalPrice = 0;
            foreach (int seatId in selectedSeats)
            {
                var seat = context.Seats.Find(seatId);
                if (seat != null)
                {
                    totalPrice += seatPrices[seat.SeatRow];
                }
            }

            // Hiển thị MessageBox xác nhận
            string message = $"Bạn có đồng ý mua {selectedSeats.Count} ghế với tổng tiền {totalPrice:N0}đ không?";
            DialogResult result = MessageBox.Show(message, "Xác nhận thanh toán",
                                                 MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // Cập nhật trạng thái ghế trong database
                foreach (int seatId in selectedSeats)
                {
                    var seat = context.Seats.Find(seatId);
                    if (seat != null)
                    {
                        seat.IsBooked = true;
                        seat.BookedDate = DateTime.Now;
                    }
                }

                // Lưu thay đổi vào database
                context.SaveChanges();

                // Cập nhật giao diện
                foreach (int seatId in selectedSeats)
                {
                    var button = khungghe.Controls.OfType<Button>()
                        .FirstOrDefault(b => (int)b.Tag == seatId);

                    if (button != null)
                    {
                        button.BackColor = Color.Red;
                        button.ForeColor = Color.White;
                        button.Enabled = false;
                    }
                }

                // Reset lại danh sách ghế đã chọn và tổng tiền
                selectedSeats.Clear();
                lblGiaTien.Text = "0đ";

                MessageBox.Show("Thanh toán thành công!", "Thông báo",
                               MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }



        private void cbphim_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cbphim.SelectedItem != null)
            {
                var selectedMovie = (Movie)cbphim.SelectedItem;
                LoadSeatsForMovie(selectedMovie.MovieId);

                // Reset lại tổng tiền và danh sách ghế đã chọn
                selectedSeats.Clear();
                UpdateTotalPrice();
            }
        }

        private void LoadSeatsForMovie(int movieId)
        {
            // Xóa tất cả ghế cũ
            khungghe.Controls.Clear();

            // Lấy tất cả ghế của phim từ database
            var seats = context.Seats
                .Where(s => s.MovieId == movieId)
                .OrderBy(s => s.SeatRow)
                .ThenBy(s => s.SeatNumber)
                .ToList();

            // Tạo button cho từng ghế
            foreach (var seat in seats)
            {
                Button seatButton = new Button
                {
                    Text = $"{seat.SeatRow}{seat.SeatNumber}",
                    Tag = seat.SeatId,
                    Dock = DockStyle.Fill
                };

                // Thiết lập màu sắc theo trạng thái ghế
                if (seat.IsBooked)
                {
                    seatButton.BackColor = Color.Red; // Đã bán
                    seatButton.ForeColor = Color.White;
                    seatButton.Enabled = false;
                }
                else
                {
                    seatButton.BackColor = Color.LightGreen; // Còn trống
                    seatButton.ForeColor = Color.Black;
                    seatButton.Enabled = true;
                }

                // Thiết lập tooltip hiển thị giá vé
                decimal price = seatPrices[seat.SeatRow];
                toolTip1.SetToolTip(seatButton, $"Ghế {seat.SeatRow}{seat.SeatNumber} - Giá: {price:N0}đ");

                // Gán sự kiện click
                seatButton.Click += SeatButton_Click;

                // Tính vị trí trong TableLayoutPanel
                int rowIndex = seat.SeatRow[0] - 'A'; // A=0, B=1, C=2...
                int colIndex = seat.SeatNumber - 1;   // 1=0, 2=1, 3=2...

                khungghe.Controls.Add(seatButton, colIndex, rowIndex);
            }
        }

        private void SeatButton_Click(object sender, EventArgs e)
        {
            Button clickedButton = sender as Button;
            int seatId = (int)clickedButton.Tag;

            if (selectedSeats.Contains(seatId))
            {
                // Bỏ chọn ghế
                selectedSeats.Remove(seatId);
                clickedButton.BackColor = Color.LightGreen;
                clickedButton.ForeColor = Color.Black;
            }
            else
            {
                // Chọn ghế
                selectedSeats.Add(seatId);
                clickedButton.BackColor = Color.Blue;
                clickedButton.ForeColor = Color.White;
            }

            // Cập nhật tổng tiền
            UpdateTotalPrice();
        }

        private void UpdateTotalPrice()
        {
            decimal totalPrice = 0;

            foreach (int seatId in selectedSeats)
            {
                var seat = context.Seats.Find(seatId);
                if (seat != null)
                {
                    totalPrice += seatPrices[seat.SeatRow];
                }
            }

            lblGiaTien.Text = $"{totalPrice:N0}đ";
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            context?.Dispose();
            base.OnFormClosed(e);
        }



    }
}