-- <PERSON><PERSON><PERSON> tạo cơ sở dữ liệu cho chương trình bán vé xem phim
-- Chạy script này trong SQL Server Management Studio

USE master;
GO

-- Tạo database nếu chưa tồn tại
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'MyWinformProjectDB')
BEGIN
    CREATE DATABASE MyWinformProjectDB;
END
GO

USE MyWinformProjectDB;
GO

-- Tạo bảng SeatPrices (Giá vé theo hàng)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SeatPrices' AND xtype='U')
BEGIN
    CREATE TABLE SeatPrices (
        SeatPriceId INT IDENTITY(1,1) PRIMARY KEY,
        SeatRow NVARCHAR(10) NOT NULL UNIQUE,
        Price DECIMAL(18,2) NOT NULL
    );
END
GO

-- Tạo bảng Movies (Phim)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Movies' AND xtype='U')
BEGIN
    CREATE TABLE Movies (
        MovieId INT IDENTITY(1,1) PRIMARY KEY,
        MovieName NVARCHAR(200) NOT NULL,
        Description NVARCHAR(500),
        CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE()
    );
END
GO

-- Tạo bảng Seats (Ghế)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Seats' AND xtype='U')
BEGIN
    CREATE TABLE Seats (
        SeatId INT IDENTITY(1,1) PRIMARY KEY,
        MovieId INT NOT NULL,
        SeatRow NVARCHAR(10) NOT NULL,
        SeatNumber INT NOT NULL,
        IsBooked BIT NOT NULL DEFAULT 0,
        BookedDate DATETIME2 NULL,
        FOREIGN KEY (MovieId) REFERENCES Movies(MovieId) ON DELETE CASCADE,
        UNIQUE (MovieId, SeatRow, SeatNumber)
    );
END
GO

-- Thêm dữ liệu giá vé
IF NOT EXISTS (SELECT * FROM SeatPrices)
BEGIN
    INSERT INTO SeatPrices (SeatRow, Price) VALUES
    ('A', 25000),
    ('B', 30000),
    ('C', 35000),
    ('D', 40000),
    ('E', 50000),
    ('F', 45000);
END
GO

-- Thêm dữ liệu phim mẫu
IF NOT EXISTS (SELECT * FROM Movies)
BEGIN
    INSERT INTO Movies (MovieName, Description) VALUES
    (N'Avengers: Endgame', N'Siêu anh hùng Marvel tập hợp để đánh bại Thanos'),
    (N'Spider-Man: No Way Home', N'Peter Parker đối mặt với đa vũ trụ'),
    (N'Top Gun: Maverick', N'Phi công huyền thoại trở lại'),
    (N'Avatar: The Way of Water', N'Cuộc phiêu lưu mới trên hành tinh Pandora');
END
GO

-- Thêm ghế cho mỗi phim (6 hàng x 10 ghế = 60 ghế/phim)
IF NOT EXISTS (SELECT * FROM Seats)
BEGIN
    DECLARE @MovieId INT;
    DECLARE @SeatRow NVARCHAR(10);
    DECLARE @SeatNumber INT;
    
    DECLARE movie_cursor CURSOR FOR 
    SELECT MovieId FROM Movies;
    
    OPEN movie_cursor;
    FETCH NEXT FROM movie_cursor INTO @MovieId;
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        -- Tạo ghế cho từng hàng A, B, C, D, E, F
        SET @SeatRow = 'A';
        WHILE @SeatRow <= 'F'
        BEGIN
            SET @SeatNumber = 1;
            WHILE @SeatNumber <= 10
            BEGIN
                INSERT INTO Seats (MovieId, SeatRow, SeatNumber, IsBooked)
                VALUES (@MovieId, @SeatRow, @SeatNumber, 0);
                
                SET @SeatNumber = @SeatNumber + 1;
            END
            
            SET @SeatRow = CHAR(ASCII(@SeatRow) + 1);
        END
        
        FETCH NEXT FROM movie_cursor INTO @MovieId;
    END
    
    CLOSE movie_cursor;
    DEALLOCATE movie_cursor;
END
GO

-- Tạo view để xem thông tin ghế kèm giá
CREATE OR ALTER VIEW vw_SeatInfo AS
SELECT 
    s.SeatId,
    s.MovieId,
    m.MovieName,
    s.SeatRow,
    s.SeatNumber,
    s.IsBooked,
    s.BookedDate,
    sp.Price,
    CONCAT(s.SeatRow, s.SeatNumber) AS SeatCode
FROM Seats s
INNER JOIN Movies m ON s.MovieId = m.MovieId
INNER JOIN SeatPrices sp ON s.SeatRow = sp.SeatRow;
GO

-- Tạo stored procedure để đặt ghế
CREATE OR ALTER PROCEDURE sp_BookSeats
    @SeatIds NVARCHAR(MAX) -- Danh sách ID ghế cách nhau bởi dấu phẩy
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Tạo bảng tạm để chứa danh sách SeatId
        CREATE TABLE #TempSeatIds (SeatId INT);
        
        -- Parse chuỗi SeatIds thành bảng
        INSERT INTO #TempSeatIds (SeatId)
        SELECT CAST(value AS INT)
        FROM STRING_SPLIT(@SeatIds, ',')
        WHERE value != '';
        
        -- Kiểm tra xem tất cả ghế có còn trống không
        IF EXISTS (
            SELECT 1 FROM Seats s
            INNER JOIN #TempSeatIds t ON s.SeatId = t.SeatId
            WHERE s.IsBooked = 1
        )
        BEGIN
            ROLLBACK TRANSACTION;
            SELECT 0 AS Success, N'Một hoặc nhiều ghế đã được đặt' AS Message;
            RETURN;
        END
        
        -- Đặt ghế
        UPDATE Seats 
        SET IsBooked = 1, BookedDate = GETDATE()
        WHERE SeatId IN (SELECT SeatId FROM #TempSeatIds);
        
        COMMIT TRANSACTION;
        SELECT 1 AS Success, N'Đặt ghế thành công' AS Message;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SELECT 0 AS Success, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO

-- Tạo stored procedure để lấy thống kê ghế
CREATE OR ALTER PROCEDURE sp_GetSeatStatistics
    @MovieId INT
AS
BEGIN
    SELECT 
        COUNT(*) AS TotalSeats,
        SUM(CASE WHEN IsBooked = 1 THEN 1 ELSE 0 END) AS BookedSeats,
        SUM(CASE WHEN IsBooked = 0 THEN 1 ELSE 0 END) AS AvailableSeats
    FROM Seats
    WHERE MovieId = @MovieId;
END
GO

PRINT N'Cơ sở dữ liệu đã được tạo thành công!';
PRINT N'- Bảng Movies: Lưu thông tin phim';
PRINT N'- Bảng Seats: Lưu thông tin ghế và trạng thái';
PRINT N'- Bảng SeatPrices: Lưu giá vé theo hàng';
PRINT N'- View vw_SeatInfo: Xem thông tin ghế kèm giá';
PRINT N'- SP sp_BookSeats: Đặt ghế';
PRINT N'- SP sp_GetSeatStatistics: Lấy thống kê ghế';
